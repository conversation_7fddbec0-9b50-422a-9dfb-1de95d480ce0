plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.ksp)
    jacoco
}

android {
    namespace = "com.bukuwarung.edc.appid"
    compileSdk = libs.versions.androidCompileSdk.get().toInt()

    testFixtures {
        enable = true
    }

    defaultConfig {
        minSdk = libs.versions.androidMinSdk.get().toInt()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        debug {
            enableUnitTestCoverage = true
        }
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.google.material)

    // Hilt
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler)

    // Testing
    testImplementation(libs.junit)
    testImplementation(libs.google.truth)
    testImplementation(libs.kotlinx.coroutines.test)
    testImplementation(libs.faker)

    testFixturesApi(libs.junit)
    testFixturesApi(libs.kotlinx.coroutines.test)
    testFixturesApi(libs.google.truth)
    testFixturesApi(libs.faker)

    androidTestImplementation(libs.androidx.test.junit)
    androidTestImplementation(libs.androidx.test.espresso.core)
}

// JaCoCo Coverage Configuration
configure<JacocoPluginExtension> {
    toolVersion = "0.8.13"
}

android.libraryVariants.configureEach {
    val variantName = name
    val testTaskName = "test${variantName.capitalize()}UnitTest"

    tasks.register<JacocoReport>("jacoco${variantName.capitalize()}Report") {
        dependsOn(testTaskName)
        group = "Reporting"
        description = "Generate Jacoco coverage reports for the ${variantName.capitalize()} build."

        reports {
            html.required.set(true)
            xml.required.set(true)
        }

        // Standard Android exclusions for JaCoCo
        val excludes = listOf(
            "**/R.class", "**/R\$*.class", "**/BuildConfig.*", "**/Manifest*.*",
            "**/*Test*.*", "android/**/*.*", "**/*\$Lambda$*.*", "**/*\$inlined$*.*",
            "**/*Module.*", "**/*Dagger*.*", "**/*Hilt*.*", "**/*MembersInjector*.*",
            "**/*_MembersInjector.class", "**/*_Factory*.*", "**/*_Provide*Factory*.*",
            "**/*Extensions*.*", "**/databinding/**", "**/android/databinding/**",
            "**/androidx/databinding/**", "**/BR.*"
        )

        // Configure class directories for both Java and Kotlin classes
        val javaClasses = fileTree(layout.buildDirectory.dir("intermediates/javac/$variantName")) {
            exclude(excludes)
        }
        val kotlinClasses = fileTree(layout.buildDirectory.dir("tmp/kotlin-classes/$variantName")) {
            exclude(excludes)
        }
        classDirectories.setFrom(files(javaClasses, kotlinClasses))

        // Set execution data location
        executionData.setFrom(
            fileTree(layout.buildDirectory.dir("jacoco")) {
                include("**/*.exec")
            }
        )

        // Configure source directories
        sourceDirectories.setFrom(files("src/main/java", "src/main/kotlin"))
        additionalSourceDirs.setFrom(files())
    }
}

tasks.register("testCoverageDebug") {
    group = "verification"
    description = "Run unit tests with coverage for debug build"
    dependsOn("testDebugUnitTest")
    finalizedBy("jacocoDebugReport")
}