package com.bukuwarung.edc.appid

import android.content.Context
import android.content.pm.PackageManager
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Default implementation of [AppIdProvider] that retrieves application information
 * from the Android package manager.
 */
@Singleton
class AppIdProviderImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : AppIdProvider {
    
    override fun getApplicationId(): String {
        return context.packageName
    }
    
    override fun getVersionCode(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionCode.toString()
        } catch (e: PackageManager.NameNotFoundException) {
            "0"
        }
    }
    
    override fun getVersionName(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName.orEmpty()
        } catch (e: PackageManager.NameNotFoundException) {
            ""
        }
    }
}
