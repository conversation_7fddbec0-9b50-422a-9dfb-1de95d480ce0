package com.bukuwarung.edc.appid

/**
 * Provider interface for application identification headers.
 * 
 * This interface provides the application ID and version information
 * that should be included in network requests for variant identification.
 */
interface AppIdProvider {
    
    /**
     * Gets the application ID for the current app variant.
     * 
     * @return Application ID (e.g., "com.bukuwarung.bukuagen.dev")
     */
    fun getApplicationId(): String
    
    /**
     * Gets the application version code.
     * 
     * @return Version code as string (e.g., "5000")
     */
    fun getVersionCode(): String
    
    /**
     * Gets the application version name.
     * 
     * @return Version name (e.g., "3.74.0")
     */
    fun getVersionName(): String
}
