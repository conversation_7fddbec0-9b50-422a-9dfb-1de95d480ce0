package com.bukuwarung.edc.appid

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * Hilt module for providing AppId dependencies.
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class AppIdModule {
    
    @Binds
    abstract fun bindAppIdProvider(
        appIdProviderImpl: AppIdProviderImpl
    ): AppIdProvider
}
