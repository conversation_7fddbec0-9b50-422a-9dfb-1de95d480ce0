package com.bukuwarung.edc.appid

import com.google.common.truth.Truth.assertThat
import org.junit.Test

class AppIdProviderImplTest {

    @Test
    fun `AppIdProvider interface has correct method signatures`() {
        // Given - interface contract verification
        val provider = FakeAppIdProvider()

        // When & Then - verify all methods return expected types
        assertThat(provider.getApplicationId()).isInstanceOf(String::class.java)
        assertThat(provider.getVersionCode()).isInstanceOf(String::class.java)
        assertThat(provider.getVersionName()).isInstanceOf(String::class.java)
    }
}

/**
 * Simple fake implementation for testing AppIdProvider interface.
 */
private class FakeAppIdProvider : AppIdProvider {
    override fun getApplicationId(): String = "com.bukuwarung.bukuagen.dev"
    override fun getVersionCode(): String = "5000"
    override fun getVersionName(): String = "3.74.0"
}

