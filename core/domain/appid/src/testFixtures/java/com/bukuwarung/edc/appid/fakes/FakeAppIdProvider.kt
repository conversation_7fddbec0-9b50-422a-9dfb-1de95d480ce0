package com.bukuwarung.edc.appid.fakes

import com.bukuwarung.edc.appid.AppIdProvider

/**
 * Fake implementation of [AppIdProvider] for testing purposes.
 * 
 * This fake allows tests to control the returned values for application ID,
 * version code, and version name.
 */
class FakeAppIdProvider(
    private val applicationId: String = "com.bukuwarung.bukuagen.dev",
    private val versionCode: String = "5000",
    private val versionName: String = "3.74.0"
) : AppIdProvider {
    
    override fun getApplicationId(): String = applicationId
    
    override fun getVersionCode(): String = versionCode
    
    override fun getVersionName(): String = versionName
}
