package com.bukuwarung.network.interceptors

import com.bukuwarung.edc.appid.AppIdProvider
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import javax.inject.Inject

/**
 * OkHttp interceptor that adds application identification headers to all network requests.
 * 
 * This interceptor adds the following headers as specified in the RFC:
 * - X-APP-ID: Application package name for variant identification
 * - X-APP-VERSION: Application version code for version tracking
 * 
 * These headers enable the backend to reliably distinguish which app variant
 * and version is making the request.
 */
class AppIdHeaderInterceptor @Inject constructor(
    private val appIdProvider: AppIdProvider
) : Interceptor {
    
    object Constants {
        const val HEADER_X_APP_ID = "X-APP-ID"
        const val HEADER_X_APP_VERSION = "X-APP-VERSION"
    }
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val newRequest = chain.request().newBuilder()
            .addHeader(Constants.HEADER_X_APP_ID, appIdProvider.getApplicationId())
            .addHeader(Constants.HEADER_X_APP_VERSION, appIdProvider.getVersionCode())
            .build()
        
        return chain.proceed(newRequest)
    }
}
