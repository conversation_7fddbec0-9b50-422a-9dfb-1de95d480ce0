package com.bukuwarung.network.refreshtoken

import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.network.BuildConfig
import com.bukuwarung.network.interceptors.AppIdHeaderInterceptor
import com.bukuwarung.network.interceptors.CrashlyticsLoggingInterceptor
import com.bukuwarung.network.interceptors.HeadersInterceptor
import com.bukuwarung.network.interceptors.SecuredInterceptor
import com.bukuwarung.network.session.SessionRemoteDataSource
import com.bukuwarung.network.utils.AppProvider
import com.moczul.ok2curl.CurlInterceptor
import com.moczul.ok2curl.logger.Logger
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import timber.log.Timber

class RefreshTokenApiProvider(
    val appProvider: AppProvider,
    val baseUrl: String = AppConfig.current.networkConfig.apiBaseUrl
) {

    val authApiService: SessionRemoteDataSource by lazy {
        val client = OkHttpClient.Builder()
            .addInterceptor(HeadersInterceptor(appProvider))
            .addInterceptor(SecuredInterceptor())
            .addInterceptor(
                CurlInterceptor(object : Logger {
                    override fun log(message: String) {
                        Timber.tag("Ok2Curl").d(message)
                    }
                })
            )
            .addNetworkInterceptor(
                HttpLoggingInterceptor().setLevel(
                    if (BuildConfig.DEBUG) {
                        HttpLoggingInterceptor.Level.BODY
                    } else {
                        HttpLoggingInterceptor.Level.NONE
                    }
                )
            )
            .addNetworkInterceptor(CrashlyticsLoggingInterceptor(appProvider))
            .build()

        Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(client)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(SessionRemoteDataSource::class.java)
    }
}
