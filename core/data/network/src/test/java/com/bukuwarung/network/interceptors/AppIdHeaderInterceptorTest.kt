package com.bukuwarung.network.interceptors

import com.bukuwarung.edc.appid.AppIdProvider
import com.google.common.truth.Truth.assertThat
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.After
import org.junit.Before
import org.junit.Test

class AppIdHeaderInterceptorTest {

    private lateinit var mockWebServer: MockWebServer
    private lateinit var fakeAppIdProvider: AppIdProvider
    private lateinit var interceptor: AppIdHeaderInterceptor
    private lateinit var client: OkHttpClient

    @Before
    fun setUp() {
        mockWebServer = MockWebServer()
        mockWebServer.start()
        
        fakeAppIdProvider = object : AppIdProvider {
            override fun getApplicationId() = "com.bukuwarung.bukuagen.dev"
            override fun getVersionCode() = "5000"
            override fun getVersionName() = "3.74.0"
        }
        
        interceptor = AppIdHeaderInterceptor(fakeAppIdProvider)
        
        client = OkHttpClient.Builder()
            .addInterceptor(interceptor)
            .build()
    }

    @After
    fun tearDown() {
        mockWebServer.shutdown()
    }

    @Test
    fun `interceptor adds X-APP-ID and X-APP-VERSION headers`() {
        // Given
        mockWebServer.enqueue(MockResponse().setResponseCode(200))
        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .build()

        // When
        client.newCall(request).execute()

        // Then
        val recordedRequest = mockWebServer.takeRequest()
        assertThat(recordedRequest.getHeader(AppIdHeaderInterceptor.Constants.HEADER_X_APP_ID))
            .isEqualTo("com.bukuwarung.bukuagen.dev")
        assertThat(recordedRequest.getHeader(AppIdHeaderInterceptor.Constants.HEADER_X_APP_VERSION))
            .isEqualTo("5000")
    }

    @Test
    fun `interceptor preserves existing headers`() {
        // Given
        mockWebServer.enqueue(MockResponse().setResponseCode(200))
        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .addHeader("Custom-Header", "custom-value")
            .addHeader("Authorization", "Bearer token123")
            .build()

        // When
        client.newCall(request).execute()

        // Then
        val recordedRequest = mockWebServer.takeRequest()
        assertThat(recordedRequest.getHeader("Custom-Header")).isEqualTo("custom-value")
        assertThat(recordedRequest.getHeader("Authorization")).isEqualTo("Bearer token123")
        assertThat(recordedRequest.getHeader(AppIdHeaderInterceptor.Constants.HEADER_X_APP_ID))
            .isEqualTo("com.bukuwarung.bukuagen.dev")
        assertThat(recordedRequest.getHeader(AppIdHeaderInterceptor.Constants.HEADER_X_APP_VERSION))
            .isEqualTo("5000")
    }

    @Test
    fun `interceptor works with different app variants`() {
        // Given
        val nusacitaProvider = object : AppIdProvider {
            override fun getApplicationId() = "com.nusacita.id.staging"
            override fun getVersionCode() = "4500"
            override fun getVersionName() = "3.73.0"
        }
        val nusacitaInterceptor = AppIdHeaderInterceptor(nusacitaProvider)
        val nusacitaClient = OkHttpClient.Builder()
            .addInterceptor(nusacitaInterceptor)
            .build()
        
        mockWebServer.enqueue(MockResponse().setResponseCode(200))
        val request = Request.Builder()
            .url(mockWebServer.url("/test"))
            .build()

        // When
        nusacitaClient.newCall(request).execute()

        // Then
        val recordedRequest = mockWebServer.takeRequest()
        assertThat(recordedRequest.getHeader(AppIdHeaderInterceptor.Constants.HEADER_X_APP_ID))
            .isEqualTo("com.nusacita.id.staging")
        assertThat(recordedRequest.getHeader(AppIdHeaderInterceptor.Constants.HEADER_X_APP_VERSION))
            .isEqualTo("4500")
    }
}
