package com.bukuwarung.network.integration

import com.bukuwarung.edc.appid.AppIdProvider
import com.bukuwarung.network.interceptors.AppIdHeaderInterceptor
import com.google.common.truth.Truth.assertThat
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.After
import org.junit.Before
import org.junit.Test

/**
 * Integration test to verify that AppIdHeaderInterceptor works correctly
 * with a real OkHttpClient configuration similar to what's used in NetworkModule.
 */
class AppIdHeaderIntegrationTest {

    private lateinit var mockWebServer: MockWebServer
    private lateinit var appIdProvider: AppIdProvider
    private lateinit var interceptor: AppIdHeaderInterceptor
    private lateinit var client: OkHttpClient

    @Before
    fun setUp() {
        mockWebServer = MockWebServer()
        mockWebServer.start()
        
        // Create a fake AppIdProvider that simulates the real implementation
        appIdProvider = object : AppIdProvider {
            override fun getApplicationId() = "com.bukuwarung.bukuagen.dev"
            override fun getVersionCode() = "5000"
            override fun getVersionName() = "3.74.0"
        }
        
        interceptor = AppIdHeaderInterceptor(appIdProvider)
        
        // Create OkHttpClient similar to NetworkModule configuration
        client = OkHttpClient.Builder()
            .addInterceptor(interceptor)
            .build()
    }

    @After
    fun tearDown() {
        mockWebServer.shutdown()
    }

    @Test
    fun `integration test - headers are added to all requests`() {
        // Given
        mockWebServer.enqueue(MockResponse().setResponseCode(200).setBody("{}"))
        val request = Request.Builder()
            .url(mockWebServer.url("/api/test"))
            .build()

        // When
        val response = client.newCall(request).execute()

        // Then
        assertThat(response.isSuccessful).isTrue()
        
        val recordedRequest = mockWebServer.takeRequest()
        assertThat(recordedRequest.getHeader("X-APP-ID")).isEqualTo("com.bukuwarung.bukuagen.dev")
        assertThat(recordedRequest.getHeader("X-APP-VERSION")).isEqualTo("5000")
        assertThat(recordedRequest.path).isEqualTo("/api/test")
    }

    @Test
    fun `integration test - headers work with POST requests`() {
        // Given
        mockWebServer.enqueue(MockResponse().setResponseCode(201).setBody("{}"))
        val request = Request.Builder()
            .url(mockWebServer.url("/api/create"))
            .post(okhttp3.RequestBody.create(null, "{}"))
            .addHeader("Content-Type", "application/json")
            .build()

        // When
        val response = client.newCall(request).execute()

        // Then
        assertThat(response.isSuccessful).isTrue()
        
        val recordedRequest = mockWebServer.takeRequest()
        assertThat(recordedRequest.getHeader("X-APP-ID")).isEqualTo("com.bukuwarung.bukuagen.dev")
        assertThat(recordedRequest.getHeader("X-APP-VERSION")).isEqualTo("5000")
        assertThat(recordedRequest.getHeader("Content-Type")).isEqualTo("application/json")
        assertThat(recordedRequest.method).isEqualTo("POST")
    }

    @Test
    fun `integration test - headers work with different app variants`() {
        // Given - Create a different app variant provider
        val nusacitaProvider = object : AppIdProvider {
            override fun getApplicationId() = "com.nusacita.id.staging"
            override fun getVersionCode() = "4500"
            override fun getVersionName() = "3.73.0"
        }
        
        val nusacitaInterceptor = AppIdHeaderInterceptor(nusacitaProvider)
        val nusacitaClient = OkHttpClient.Builder()
            .addInterceptor(nusacitaInterceptor)
            .build()
        
        mockWebServer.enqueue(MockResponse().setResponseCode(200).setBody("{}"))
        val request = Request.Builder()
            .url(mockWebServer.url("/api/nusacita"))
            .build()

        // When
        val response = nusacitaClient.newCall(request).execute()

        // Then
        assertThat(response.isSuccessful).isTrue()
        
        val recordedRequest = mockWebServer.takeRequest()
        assertThat(recordedRequest.getHeader("X-APP-ID")).isEqualTo("com.nusacita.id.staging")
        assertThat(recordedRequest.getHeader("X-APP-VERSION")).isEqualTo("4500")
    }

    @Test
    fun `integration test - multiple requests all include headers`() {
        // Given
        repeat(3) {
            mockWebServer.enqueue(MockResponse().setResponseCode(200).setBody("{}"))
        }

        // When - Make multiple requests
        repeat(3) { index ->
            val request = Request.Builder()
                .url(mockWebServer.url("/api/request-$index"))
                .build()
            
            val response = client.newCall(request).execute()
            assertThat(response.isSuccessful).isTrue()
        }

        // Then - All requests should have headers
        repeat(3) { index ->
            val recordedRequest = mockWebServer.takeRequest()
            assertThat(recordedRequest.getHeader("X-APP-ID")).isEqualTo("com.bukuwarung.bukuagen.dev")
            assertThat(recordedRequest.getHeader("X-APP-VERSION")).isEqualTo("5000")
            assertThat(recordedRequest.path).isEqualTo("/api/request-$index")
        }
    }
}
