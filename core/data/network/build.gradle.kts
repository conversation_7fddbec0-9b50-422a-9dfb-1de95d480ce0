import com.android.build.api.variant.BuildConfigField
import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.hilt)
    alias(libs.plugins.ksp)
    alias(libs.plugins.mokkery)
    jacoco
}

android {
    namespace = "com.bukuwarung.network"
    compileSdk = libs.versions.androidCompileSdk.get().toInt()

    buildFeatures {
        buildConfig = true
    }
    testFixtures {
        enable = true
    }

    defaultConfig {
        minSdk = libs.versions.androidMinSdk.get().toInt()
        multiDexEnabled = true
        vectorDrawables.useSupportLibrary = true

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    testOptions {
        targetSdk = libs.versions.androidTargetSdk.get().toInt()
    }

    buildTypes {
        debug {
            enableUnitTestCoverage = true
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            resValue("string", "clear_text_config", "true")
        }

        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            resValue("string", "clear_text_config", "false")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    buildFeatures {
        viewBinding = true
    }
}

kotlin {
    compilerOptions {
        jvmTarget = JvmTarget.JVM_17
    }
}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.hilt.android)
    implementation(projects.core.domain.appConfig)
    implementation(project(":core:domain:appid"))
    implementation(project(":core:utils:result"))
    ksp(libs.hilt.compiler)

    implementation(libs.ok2curl)
    implementation(libs.google.gson)
    implementation(libs.bundles.retrofit)
    implementation(libs.timber)

    testFixturesApi(libs.junit)
    testFixturesApi(libs.kotlinx.coroutines.test)
    testFixturesApi(libs.google.truth)
    testFixturesApi(libs.okhttp.mockwebserver)
    testFixturesApi(libs.faker)
}

// JaCoCo Coverage Configuration
configure<JacocoPluginExtension> {
    toolVersion = "0.8.13"
}

android.libraryVariants.configureEach {
    val variantName = name
    val testTaskName = "test${variantName.capitalize()}UnitTest"

    tasks.register<JacocoReport>("jacoco${variantName.capitalize()}Report") {
        dependsOn(testTaskName)
        group = "Reporting"
        description = "Generate Jacoco coverage reports for the ${variantName.capitalize()} build."

        reports {
            html.required.set(true)
            xml.required.set(true)
        }

        val excludes = listOf(
            "**/R.class", "**/R\$*.class", "**/BuildConfig.*", "**/Manifest*.*",
            "**/*Test*.*", "android/**/*.*", "**/*\$Lambda$*.*", "**/*\$inlined$*.*",
            "**/*Module.*", "**/*Dagger*.*", "**/*Hilt*.*", "**/*MembersInjector*.*",
            "**/*_MembersInjector.class", "**/*_Factory*.*", "**/*_Provide*Factory*.*",
            "**/*Extensions*.*", "**/databinding/**", "**/android/databinding/**",
            "**/androidx/databinding/**", "**/BR.*"
        )

        val javaClasses = fileTree(layout.buildDirectory.dir("intermediates/javac/$variantName")) {
            exclude(excludes)
        }
        val kotlinClasses = fileTree(layout.buildDirectory.dir("tmp/kotlin-classes/$variantName")) {
            exclude(excludes)
        }
        classDirectories.setFrom(files(javaClasses, kotlinClasses))

        executionData.setFrom(
            fileTree(layout.buildDirectory.dir("jacoco")) {
                include("**/*.exec")
            }
        )

        additionalSourceDirs.setFrom(files("src/main/java", "src/main/kotlin"))
    }
}

// Task for running tests with coverage for a specific variant
tasks.register("testCoverageDebug") {
    group = "verification"
    description = "Run unit tests with coverage for debug build"
    dependsOn("testDebugUnitTest")
    finalizedBy("jacocoDebugReport")
}
