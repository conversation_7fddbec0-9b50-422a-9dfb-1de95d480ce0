pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        google()
        mavenCentral()
        maven("https://maven.aliyun.com/nexus/content/groups/public/")
        maven("https://maven.zohodl.com")
        maven("https://dl.google.com/dl/android/maven2/")
        maven("https://jitpack.io")
        maven("https://packages.bureau.id/api/packages/Bureau/maven")
    }
}
rootProject.name = "EDC"

// Function to recursively include all Gradle projects in the core directory
fun includeDirectory(dirName: String) {
    val dir = File(rootDir, dirName)
    if (dir.exists() && dir.isDirectory()) {
        scanDirectoryForProjects(dir, dirName)
    } else {
        println("$dirName directory not found at: ${dir.absolutePath}")
    }
}

// Recursive helper function to scan directories for Gradle projects
fun scanDirectoryForProjects(directory: File, pathPrefix: String) {
    directory.listFiles()?.forEach { file ->
        if (file.isDirectory()) {
            val buildFile = File(file, "build.gradle")
            val buildFileKts = File(file, "build.gradle.kts")

            // Check if it's a valid Gradle project (has build.gradle or build.gradle.kts)
            if (buildFile.exists() || buildFileKts.exists()) {
                val projectName = ":$pathPrefix:${file.name}"
                include(projectName)
                println("Included project: $projectName")
            }

            // Recursively scan subdirectories regardless of whether current dir is a project
            scanDirectoryForProjects(file, "$pathPrefix:${file.name}")
        }
    }
}
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

include(":app")
include(":ui-component")
include(":bluetooth-devices-setup")
includeDirectory("core")
include(":core:domain:app-config")
include(":core:domain:appid")
