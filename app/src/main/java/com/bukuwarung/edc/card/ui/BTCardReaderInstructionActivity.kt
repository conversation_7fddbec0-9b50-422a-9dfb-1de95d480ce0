package com.bukuwarung.edc.card.ui

import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.app.ActivityCompat
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.bluetooth_printer.BluetoothDevices.hasPairedPrinter
import com.bukuwarung.bluetooth_printer.activities.print.setup.FirmwareUpgradeActivity
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity.Companion.DEVICE_SN_LIST
import com.bukuwarung.bluetooth_printer.base.BaseActivity
import com.bukuwarung.bluetooth_printer.utils.BluetoothConnection
import com.bukuwarung.bluetooth_printer.utils.PermissionConst
import com.bukuwarung.bluetooth_printer.utils.PermissionConst.ACCESS_LOCATION_SETTINGS
import com.bukuwarung.bluetooth_printer.utils.PermissionUtils.hasBluetoothPermission
import com.bukuwarung.bluetooth_printer.utils.PermissionUtils.hasLocationPermission
import com.bukuwarung.bluetooth_printer.utils.PermissionUtils.isSystemLocationServiceEnabled
import com.bukuwarung.bluetooth_printer.utils.PermissionUtils.requestLocationPermission
import com.bukuwarung.bluetooth_printer.utils.Utility.showDialogIfActivityAlive
import com.bukuwarung.bluetooth_printer.utils.hideView
import com.bukuwarung.cardreader.CardReaderHelper
import com.bukuwarung.cardreader.CardReaderType
import com.bukuwarung.cardreader.dto.CardReaderInfo
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.ZohoChatEntryPoint
import com.bukuwarung.edc.app.config.types.AppVariant
import com.bukuwarung.edc.card.data.model.ActivationResponse
import com.bukuwarung.edc.card.data.model.Terminal
import com.bukuwarung.edc.card.data.model.TerminalPairedDevice
import com.bukuwarung.edc.card.domain.model.TmsFailureType
import com.bukuwarung.edc.databinding.ActivityInitBtCardReaderBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.constant.HomePageAnalyticsConstants.ALLOWED_LOCATION_ACCESS_POP_UP
import com.bukuwarung.edc.homepage.constant.HomePageAnalyticsConstants.PAIR_SAKU_POP_UP
import com.bukuwarung.edc.homepage.constant.HomePageAnalyticsConstants.SLEEP_MODE
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.homepage.ui.home.HomePageViewModel
import com.bukuwarung.edc.payments.data.model.CardErrorType
import com.bukuwarung.edc.payments.ui.dialog.CardErrorDialog
import com.bukuwarung.edc.printer.ui.dialog.BukuDialog
import com.bukuwarung.edc.util.DeviceUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.getUserRegisteredDevices
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.openActivityAndFinish
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.worker.location.LocationUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.util.UUID
import javax.inject.Inject

@AndroidEntryPoint
class BTCardReaderInstructionActivity : BaseActivity() {

    private lateinit var binding: ActivityInitBtCardReaderBinding

    @Inject
    lateinit var locationUtil: LocationUtil

    @Inject
    lateinit var appVariant: AppVariant

    private var intentRequestCode = 0
    private var bluetoothConnection: BluetoothConnection? = null
    private var deviceType = CARD_READER
    private var errorDialog: CardErrorDialog? = null
    private var transactionType = TransactionType.BALANCE_INQUIRY.type
    private var data = Bundle()
    private var btCardReader = CardReaderHelper.getInstance()

    private val homePageViewModel: HomePageViewModel by viewModels()
    private val edcCardViewModel: EdcCardViewModel by viewModels()
    private val btCardReaderViewModel: TerminalManagementViewModel by viewModels()

    override fun setViewBinding() {
        binding = ActivityInitBtCardReaderBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        Log.d("--->", "onCreate called $localClassName")

        transactionType = if (intent.hasExtra("mode") && intent.getStringExtra("mode")
                ?.equals(TransactionType.TRANSFER_INQUIRY.type) == true
        ) {
            TransactionType.TRANSFER_INQUIRY.type
        } else if (intent.hasExtra("mode") && intent.getStringExtra("mode")
                ?.equals(TransactionType.CASH_WITHDRAWAL.type) == true
        ) {
            TransactionType.CASH_WITHDRAWAL.type
        } else {
            TransactionType.BALANCE_INQUIRY.type
        }
        deviceType = intent.getStringExtra(DEVICE_TYPE) ?: CARD_READER
        bluetoothConnection = BluetoothConnection(this)
        intentRequestCode = intent.getIntExtra("requestCode", 0)
        // check for location permission
        checkLocationPermissionAndProceedLogic()
        Utils.sharedPreferences.put(Utils.FIRST_TIME_INVALID_PIN, true)
    }

    private fun checkLocationPermissionAndProceedLogic() {
        if (hasLocationPermission()) {
            Log.d("--->", "Location permission granted")
            if (isSystemLocationServiceEnabled(this)) {
                if (btCardReader.isDeviceConnected() && BluetoothDevices.hasPairedCardReader() &&
                    bluetoothConnection?.isEnabled == true
                ) {
                    // Proceed with the logic as Bluetooth is enabled and pairedCardReaderList is not empty
                    checkPermissionAndBluetooth()
                } else if (btCardReader.isDeviceConnected()) {
                    // If Tianyu BT device is already connected, proceed with device logon
                    deviceLogon()
                } else {
                    // Show dialog as Bluetooth is not enabled or pairedCardReaderList is empty
                    showNoCardReaderPairedDialog()
                }
            } else {
                showRequestEnableGpsDialog()
            }
        } else {
            Log.d("--->", "Location permission denied")
            confirmLocationPermission()
        }
    }

    private fun promptEnableLocationServices() {
        val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
        startActivityForResult(intent, ACCESS_LOCATION_SETTINGS)
    }

    override fun subscribeState() {
        observeLogonData()
    }

    override fun onStart() {
        super.onStart()
        bluetoothConnection?.onStart()
    }

    override fun onResume() {
        super.onResume()
        Log.d("--->", "onResume called $localClassName")
    }

    private fun fetchLogonDataAfterGettingLatLong() {
        lifecycleScope.launch {
            // first it will fetch location
            try {
                val location = locationUtil.getCurrentLocation(this@BTCardReaderInstructionActivity)
                val lat = location?.latitude?.toFloat()
                val long = location?.longitude?.toFloat()
                Log.d("--->", "lat: $lat, long: $long")
                Utils.safeLet(lat, long) { latitude, longitude ->
                    if (Utils.isSerialNumberCheckEnabled()) {
                        locationUtil.storeLocation(longitude, latitude)
                        locationUtil.storeCardTransactionLocation(
                            btCardReader.getCardReaderInfo().deviceSn,
                            transactionType,
                            longitude,
                            latitude
                        )
                    }
                }
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
            homePageViewModel.fetchLogonData(Utils.getTerminalId())
        }
    }

    private fun showPermissionDeniedDialog() {
        // ask use to enable location permission from app info
        val dialog = CardErrorDialog(this, CardErrorType.LOCATION_DENIED) {
            val intent =
                Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.fromParts("package", packageName, null)
                }
            startActivityForResult(intent, ACCESS_LOCATION_SETTINGS)
        }
        dialog.show()
    }

    private fun initViews() {
    }

    private fun checkPermissionAndBluetooth() {
        Log.d("--->BT", "checkPermissionAndBluetooth")
        Log.d("--->BT", "hasBluetoothPermission: ${!hasBluetoothPermission()}")
        Log.d(
            "--->BT",
            "bluetoothConnection?.isEnabled: ${(bluetoothConnection?.isEnabled == false)}"
        )
        val pairedCardReaderList = BluetoothDevices.getPairedCardReaderList()
        Log.d(
            "--->",
            "pairedCardReaderList : ${
                pairedCardReaderList?.forEach(
                    ::println
                )
            } <==> pairedCardReaderList size: ${pairedCardReaderList?.size}"
        )
        Log.d(
            "--->",
            "Utils.getBooleanConfig(\"hasCheckedBT\") = ${Utils.getBooleanConfig("hasCheckedBT")}"
        )
        Log.d(
            "--->",
            "Utils.getBooleanConfig(\"card_reader_iniatiated\") = ${
                Utils.getBooleanConfig(
                    "card_reader_iniatiated"
                )
            }"
        )
        Log.d("--->", "BTCardReader.isConnected = ${btCardReader.isDeviceConnected()}")
        if (bluetoothConnection?.isEnabled == false || !hasBluetoothPermission()) {
            val device = getUserRegisteredDevices()
            device.map { it.serialNumber }
            errorDialog = CardErrorDialog(
                context = this,
                errorType = CardErrorType.NO_BLUETOOTH
            ) {
                openSetupBluetoothDeviceActivityForCardReader()
            }
            showDialogIfActivityAlive(this, errorDialog)
        } else if (pairedCardReaderList.isNullOrEmpty()) {
            openSetupBluetoothDeviceActivityForCardReader()
        } else if (!Utils.getBooleanConfig("hasCheckedBT")) {
            if (!Utils.getBooleanConfig("card_reader_iniatiated") ||
                !btCardReader.isDeviceConnected()
            ) {
                Log.d("--->", "connectDevice would be called")
                connectDevice()
            } else {
                Log.d("--->", "deviceLogon would be called")
                deviceLogon()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        errorDialog?.dismiss()
        when (requestCode) {
            0 -> {
                initViews()
            }

            REQUEST_TO_ENABLE_BT -> {
                initViews()
            }

            ACCESS_LOCATION_SETTINGS -> {
                checkLocationPermissionAndProceedLogic()
            }

            else -> {
                finish()
            }
        }
    }

    private fun connectDevice() {
        if (Utils.sharedPreferences.get("edcConnected", false)) {
            return
        }
        Log.d("connect", "starting connection")
        if (!Utils.getBooleanConfig("card_reader_iniatiated")) {
            Log.d("connect", "starting init")
            val name = BluetoothDevices.getPairedCardReaderList()?.get(0)?.name
            if (name?.startsWith("MP-") == true) {
                Utils.sharedPreferences.put("bt_upgrade", true)
                btCardReader?.initCardReaderHelper(CardReaderType.MOREFUN)
            } else {
                btCardReader?.initCardReaderHelper(CardReaderType.TIANYU)
            }
            Utils.sharedPreferences.put("card_reader_iniatiated", true)
        }
        Thread {
            var isConnected: Boolean = false
            if (btCardReader?.isDeviceConnected() == true) {
                Log.d("connect", "already connect")
                isConnected = true
            } else {
                Log.d("connect", "connecting")
                isConnected = BluetoothDevices.getPairedCardReader()
                    ?.let { btCardReader?.connectToDevice(it) } == true
            }
            Utils.sharedPreferences.put("edcConnected", isConnected)

            runOnUiThread {
                if (isConnected) {
                    deviceLogon()
                } else {
                    showNoCardReaderPairedDialog()
                }
            }
        }.start()
    }

    private fun deviceLogon() {
        var isFirmwareUpgradeRequired = false
        try {
            isFirmwareUpgradeRequired = btCardReader.isFirmwareUpgradeRequired()
            if (!isFirmwareUpgradeRequired) {
                val deviceList = getUserRegisteredDevices()
                val deviceInfo: CardReaderInfo = btCardReader.getCardReaderInfo()
                deviceList.forEach { device ->
                    if (device.serialNumber == deviceInfo.deviceSn) {
                        device.paymentAccountId?.let { Utils.setPaymentAccountId(it) }
                        device.terminalId?.let { Utils.setTerminalId(it) }
                    }
                }

                // Add partnership check for fixed terminals
                val connectedDeviceItem =
                    deviceList.firstOrNull { it.serialNumber == deviceInfo.deviceSn }
                if (Utils.shouldRestrictPartnership(connectedDeviceItem)) {
                    restrictPartnershipDialog()
                    return
                }

                binding.tvLogon.text = getString(R.string.verifying_card_reader)
                if (!Utils.isSerialNumberCheckEnabled()) {
                    edcCardViewModel.configureEmv()
                    binding.tvLogon.text = "Otorisasi EDC Saku.."
                    fetchLogonDataAfterGettingLatLong()
                } else {
                    if (Utils.getBooleanConfig("pair" + BluetoothDevices.getPairedCardReader())) {
                        updateTerminalDetails()
                        edcCardViewModel.configureEmv()
                    } else {
                        btCardReaderViewModel.registerPairedDevice(
                            deviceInfo.deviceSn,
                            BluetoothDevices.getPairedCardReaderList()?.get(0)!!.name,
                            BluetoothDevices.getPairedCardReader()!!,
                            DeviceUtils.getAndroidId(this)
                        )
                    }
                }
                binding.tvLogon.text = "Otorisasi EDC Saku.."
            } else {
                openActivityAndFinish(FirmwareUpgradeActivity::class.java)
            }
        } catch (e: Exception) {
            showCardReaderInSleepmodeError()
            bwLog(e)
        }
    }

    private fun updateTerminalDetails() {
        val packageInfo = packageManager.getPackageInfo(packageName, 0)
        val versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            packageInfo.longVersionCode
        } else {
            @Suppress("DEPRECATION")
            packageInfo.versionCode.toLong()
        }
        val versionName = packageInfo.versionName.orEmpty()

        val cardReaderInfo = btCardReader.getCardReaderInfo()
        if (cardReaderInfo.deviceSn == null) {
            showNoCardReaderPairedDialog()
        } else {
            val additionalDetails = mapOf(
                "Manufacturer" to Build.MANUFACTURER,
                "Model" to Build.MODEL,
                "AppVersion" to versionName
            )

            val terminal = Terminal(
                serialNumber = cardReaderInfo.deviceSn,
                btName = BluetoothDevices.getPairedCardReaderList()?.get(0)?.name,
                btAddress = BluetoothDevices.getPairedCardReader(),
                appDeviceId = Analytics.getDeviceId(),
                appVersion = versionCode.toString(),
                androidId = DeviceUtils.getAndroidId(this),
                hardwareVersion = cardReaderInfo.hardwareVersion,
                softwareVersion = cardReaderInfo.softwareVersion,
                status = "Inactive",
                currentLocationId = "0",
                manufacturer = cardReaderInfo.manufacturer,
                model = cardReaderInfo.devicePn,
                reactivationKey = "",
                additionalDetails = additionalDetails
            )
            btCardReaderViewModel.updateTerminal(cardReaderInfo.deviceSn, terminal)
        }
    }

    @SuppressLint("MissingPermission")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        val firstResult = grantResults.getOrNull(0)
        grantResults.getOrNull(1)
        when (requestCode) {
            PermissionConst.ACCESS_LOCATION -> {
                if (firstResult == PackageManager.PERMISSION_GRANTED) {
                    // show dialog to connect card reader
                    showNoCardReaderPairedDialog()
                    // No need to request bluetooth permission here, as it will be requested in the next step
                } else {
                    // check if user has denied the permission
                    val shouldShowRequestPermissionRationale =
                        ActivityCompat.shouldShowRequestPermissionRationale(
                            this,
                            android.Manifest.permission.ACCESS_FINE_LOCATION
                        )
                    // permission request denied
                    if (shouldShowRequestPermissionRationale) {
                        // user denied permission for first time.
                        // finish the activity
                        Toast.makeText(
                            this,
                            "Beri izin akses untuk dapat melanjutkan",
                            Toast.LENGTH_SHORT
                        ).apply {
                            setGravity(Gravity.CENTER, 0, 0)
                        }.show()
                        Handler(Looper.getMainLooper()).postDelayed({
                            finish()
                        }, 3000)
                    } else {
                        // permission denied for 2nd time and user has checked "never ask again"
                        showPermissionDeniedDialog()
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        bluetoothConnection = null
    }

    override fun onStop() {
        super.onStop()
        bluetoothConnection?.onStop()
    }

    private fun observeLogonData() {
        btCardReaderViewModel.tmsError.observe(this) {
            binding.tvLogon.hideView()
            Log.d("terminal_error", it?.error.toString())

            when (it?.error?.type) {
                TmsFailureType.TERMINAL_NOT_CONFIGURED -> {
                    showActivationError(it.error.code, true)
                }

                TmsFailureType.UNAUTHORIZED -> {
                    // should be not happen here, refresh token already done in TokenAuthenticator
                    bwLog(
                        "Unauthorized error in BTCardReaderInstructionActivity: ${it.error.message}"
                    )
                }

                TmsFailureType.ACTIVATION_FAILED_BE,
                TmsFailureType.FIREBASE_FAILED_LOADING_KEY,
                TmsFailureType.ACTIVATION_FAILURE -> {
                    forceActivateDevice()
                }

                else -> checkAndShowTerminalActivationState()
            }
        }
        btCardReaderViewModel.tmsResponse.observe(this) {
            when (it.status) {
                Status.LOADING -> {
                    binding.tvLogon.showView()
                }

                Status.SUCCESS -> {
                    if (it.data is Terminal) {
                        binding.tvLogon.text = "Verifikasi berhasil."
                        if (it.data.status == "Active") {
                            // inject masterKey in device
                            if (!it.data.reactivationKey.isNullOrBlank()) {
                                it.data.reactivationKey?.let { key ->
                                    Utils.setConnectedDeviceMasterKey(key, it.data.serialNumber)
                                    Utils.setTerminalMasterKey(key)
                                }
                            }
                            fetchLogonDataAfterGettingLatLong()
                        } else if (it.data.status == "Disabled") {
                            Log.d("terminal", "disabled")
                            when (appVariant) {
                                AppVariant.ATM_PRO, AppVariant.NUSA_CITA ->
                                    showActivationError("E21", true)

                                AppVariant.BUKU -> showTerminalIdOrDeviceSerialBlockedError()
                            }
                        } else if (it.data.status == "Inactive") {
                            binding.tvLogon.text =
                                getString(R.string.activating_device)
                            if (!it.data.reactivationKey.isNullOrBlank()) {
                                it.data.reactivationKey?.let { key ->
                                    Utils.setConnectedDeviceMasterKey(key, it.data.serialNumber)
                                    Utils.setTerminalMasterKey(key)
                                    btCardReaderViewModel.updateTerminalStatus(
                                        it.data.serialNumber,
                                        "Active"
                                    )
                                    fetchLogonDataAfterGettingLatLong()
                                }
                            } else {
                                btCardReaderViewModel.activateTerminal(
                                    btCardReader.getCardReaderInfo().deviceSn,
                                    UUID.randomUUID().toString()
                                )
                            }
                        }
                    } else if (it.data is TerminalPairedDevice) {
                        binding.tvLogon.text = "Verifikasi berhasil."
                        Utils.sharedPreferences.put(
                            "pair" + BluetoothDevices.getPairedCardReader(),
                            true
                        )
                        deviceLogon()
                    } else if (it.data is ActivationResponse) {
                        getString(R.string.activating_device)
                    }
                }

                Status.ERROR, Status.NO_INTERNET -> {
                    binding.tvLogon.hideView()
                    val dialog = BukuDialog(
                        context = this@BTCardReaderInstructionActivity,
                        title = getString(R.string.failed_to_load),
                        subTitle =
                        getString(R.string.checking_internet_connection_message) +
                            "[${it.message}]",
                        image = null,
                        isLoader = false,
                        btnLeftListener = {
                            goToDestination(HomePageActivity::class.java)
                        },
                        btnRightListener = {
                            fetchLogonDataAfterGettingLatLong()
                        },
                        btnLeftText = getString(R.string.back),
                        btnRightText = getString(R.string.retry)
                    )
                    showDialogIfActivityAlive(this, dialog)
                }
            }
        }

        homePageViewModel.logonData.observe(this) {
            when (it.status) {
                Status.LOADING -> {
                    binding.tvLogon.showView()
                }

                Status.SUCCESS -> {
                    binding.tvLogon.hideView()
                    if (hasPairedPrinter()) {
                        redirectToChooseAccountType()
                    } else {
                        showPrinterConnectionDialog()
                    }
                }

                Status.ERROR -> {
                    if ((it.message?.contains("TMK", ignoreCase = true) == true) ||
                        (it.message?.contains("422", ignoreCase = true) == true)
                    ) {
                        showAuthenticationError(it.message)
                    } else {
                        showSystemTrouble(it.message)
                    }
                }

                Status.NO_INTERNET -> {
                    binding.tvLogon.hideView()
                    val dialog = BukuDialog(
                        context = this@BTCardReaderInstructionActivity,
                        title = getString(R.string.failed_to_load),
                        subTitle =
                        getString(R.string.checking_internet_connection_message) +
                            "[${it.message}]",
                        image = null,
                        isLoader = false,
                        btnLeftListener = {
                            goToDestination(HomePageActivity::class.java)
                        },
                        btnRightListener = {
                            fetchLogonDataAfterGettingLatLong()
                        },
                        btnLeftText = getString(R.string.back),
                        btnRightText = getString(R.string.retry)
                    )
                    showDialogIfActivityAlive(this, dialog)
                }
            }
        }
    }

    private fun forceActivateDevice() {
        Utils.setConnectedDeviceMasterKey("", btCardReader.getCardReaderInfo().deviceSn)
        updateTerminalDetails()
    }

    private fun showPrinterConnectionDialog() {
        var dialog: BukuDialog? = null
        dialog = BukuDialog(
            context = this,
            title = getString(R.string.connect_to_bluetooth_printer),
            subTitle = getString(R.string.connect_to_bluetooth_printer_message),
            image = R.drawable.ic_printing,
            isLoader = false,
            btnLeftListener = {
                dialog?.dismiss()
                redirectToChooseAccountType()
            },
            btnRightListener = {
                dialog?.dismiss()
                openActivityAndFinish(SetupBluetoothDeviceActivity::class.java) {
                    putString(
                        SetupBluetoothDeviceActivity.DEVICE_TYPE,
                        SetupBluetoothDeviceActivity.PRINTER
                    )
                }
            },
            btnLeftText = "Lanjut Transaksi",
            btnRightText = "Hubungkan"
        )
        showDialogIfActivityAlive(this, dialog)
    }

    private fun redirectToChooseAccountType() {
        openActivity(ChooseAccountTypeActivity::class.java) {
            data.putString(
                Constant.INTENT_KEY_TRANSACTION_TYPE,
                transactionType
            )
            putBundle("track", data)
        }
        finish()
    }

    private fun showNoCardReaderPairedDialog() {
        fun AppVariant.btnRightText(): String = when (this) {
            AppVariant.ATM_PRO, AppVariant.NUSA_CITA -> getString(
                R.string.connect_printer
            )

            AppVariant.BUKU -> getString(R.string.connect_now)
        }

        var dialog: BukuDialog? = null
        dialog = BukuDialog(
            context = this,
            title = getString(R.string.connect_edc_bt_card_reader),
            subTitle = getString(R.string.connect_miniatm_application_via_bluetooth),
            image = R.drawable.ic_bluetooth_not_connected,
            isLoader = false,
            btnLeftListener = {
                goToDestination(HomePageActivity::class.java)
            },
            btnRightListener = {
                Analytics.trackEventMobile(PAIR_SAKU_POP_UP)
                dialog?.dismiss()
                checkPermissionAndBluetooth()
            },
            btnLeftText = "Nanti Saja",
            btnRightText = appVariant.btnRightText()
        )
        showDialogIfActivityAlive(this, dialog)
    }

    private fun confirmLocationPermission() {
        // notify user that location permission is required
        val dialog = BukuDialog(
            context = this,
            title = getString(R.string.permit_location_access),
            subTitle = getString(R.string.permit_location_access_message),
            image = null,
            isLoader = false,
            btnRightListener = {
                requestLocationPermission(this)
            },
            btnLeftListener = {},
            btnRightText = "Lanjut",
            btnLeftText = ""
        )
        showDialogIfActivityAlive(this, dialog)
    }

    private fun showRequestEnableGpsDialog() {
        val dialog = BukuDialog(
            context = this,
            title = getString(R.string.location_not_detected),
            subTitle = getString(R.string.location_not_detected_message),
            image = R.drawable.img_location_not_found,
            isLoader = false,
            btnLeftListener = {},
            btnRightListener = {
                Analytics.trackEventMobile(ALLOWED_LOCATION_ACCESS_POP_UP)
                promptEnableLocationServices()
            },
            btnLeftText = "",
            btnRightText = getString(R.string.aktifkan_lokasi)
        )
        showDialogIfActivityAlive(this, dialog)
    }

    private fun checkAndShowTerminalActivationState() {
        val inactiveTerminal: CardErrorDialog? = ErrorMapping.showErrorDialog(
            this,
            null,
            ErrorMapping.terminalNotActivatedErrorCode[0]
        ) {
            ZohoChat.openZohoChat(ZohoChat.CARD_READER_INSTRUCTION)
        }
        showDialogIfActivityAlive(this, inactiveTerminal)
    }

    private fun openSetupBluetoothDeviceActivityForCardReader() {
        val device = getUserRegisteredDevices()
        val deviceSerialNumberList = device.map { it.serialNumber }
        openActivityAndFinish(SetupBluetoothDeviceActivity::class.java) {
            putString(
                SetupBluetoothDeviceActivity.DEVICE_TYPE,
                SetupBluetoothDeviceActivity.CARD_READER
            )
            putStringArrayList(DEVICE_SN_LIST, ArrayList(deviceSerialNumberList))
        }
    }

    private fun showCardReaderInSleepmodeError() {
        Analytics.trackEventMobile(SLEEP_MODE)
        val dialog = CardErrorDialog(
            context = this,
            errorType = CardErrorType.BLUETOOTH_DEVICE_IN_SLEEPMODE
        ) {
            deviceLogon()
        }
        showDialogIfActivityAlive(this, dialog)
    }

    private fun showSystemTrouble(errorCode: String?) {
        BukuDialog(
            context = this@BTCardReaderInstructionActivity,
            title = getString(R.string.system_error),
            subTitle = getString(R.string.system_trouble_message, errorCode),
            image = R.drawable.ic_system_error,
            isLoader = false,
            btnLeftListener = {
                openActivity(HomePageActivity::class.java)
                finish()
            },
            btnRightListener = {},
            btnLeftText = getString(R.string.back),
            btnRightText = ""
        ).show()
    }

    private fun showActivationError(errorCode: String?, isCustomerSupportRequired: Boolean) {
        errorDialog = ErrorMapping.showErrorDialog(
            this,
            null,
            errorCode,
            dismissListener = {
                if (isCustomerSupportRequired) {
                    ZohoChat.openZohoChat(ZohoChat.CARD_READER_INSTRUCTION)
                } else {
                    openActivity(HomePageActivity::class.java)
                }
            }

        )
        showDialogIfActivityAlive(this, errorDialog)
    }

    private fun showAuthenticationError(errorCode: String?) {
        val buttonText = when (appVariant) {
            AppVariant.ATM_PRO, AppVariant.NUSA_CITA -> R.string.understand
            AppVariant.BUKU -> R.string.contact_customer_care
        }
        fun AppVariant.onRightButtonClicked() {
            when (this) {
                AppVariant.ATM_PRO, AppVariant.NUSA_CITA -> goToDestination(
                    HomePageActivity::class.java
                )

                AppVariant.BUKU -> {
                    ZohoChat.openZohoChat("bt_card_read_instruction")
                    finish()
                }
            }
        }
        BukuDialog(
            context = this@BTCardReaderInstructionActivity,
            title = getString(R.string.authentication_trouble),
            subTitle = getString(R.string.authentication_trouble_message, errorCode),
            image = R.drawable.ic_system_error,
            isLoader = false,
            btnLeftListener = {},
            btnRightListener = {
                appVariant.onRightButtonClicked()
            },
            btnLeftText = "",
            btnRightText = getString(buttonText)
        ).show()
    }

    private fun showTerminalIdOrDeviceSerialBlockedError() {
        val dialog = CardErrorDialog(
            context = this,
            errorType = CardErrorType.TERMINAL_ID_OR_DEVICE_SERIAL_BLOCKED,
            stan = ErrorMapping.TERMINAL_ID_OR_DEVICE_SERIAL_BLOCKED,
            positiveListener = {
                goToDestination(HomePageActivity::class.java)
                ZohoChat.openZohoChat(ZohoChatEntryPoint.TERMINAL_ID_OR_DEVICE_SERIAL_BLOCKED)
            },
            dismissListener = {
                goToDestination(HomePageActivity::class.java)
            }
        )
        showDialogIfActivityAlive(this, dialog)
    }

    private fun restrictPartnershipDialog() {
        val dialog = CardErrorDialog(
            this,
            CardErrorType.RESTRICT_PARTNERSHIP_USERS,
            dismissListener = {
                Utils.clearDataAndLogout(false)
            }
        )
        Utils.showDialogIfActivityAlive(this, dialog)
    }

    companion object {
        private const val REQUEST_TO_ENABLE_BT = 11
        private const val CARD_READER = "card_reader"
        private const val DEVICE_TYPE = "device_type"
    }
}
