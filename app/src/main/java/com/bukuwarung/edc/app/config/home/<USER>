package com.bukuwarung.edc.app.config.home

import com.bukuwarung.edc.app.config.HomeConfig
import com.bukuwarung.edc.util.Utils

/**
 * Home configuration for Buku variant
 * Provides Buku-specific home page settings with yellow branding and standard BukuAgen behavior
 */
class BukuHomeConfig : HomeConfig {
    override val appDisplayName: String = "BukuAgen"
    override val defaultStoreName: String = "BukuAgen EDC"
    override val shouldShowLogo: Boolean = true // Show ivLogo and vwSeparator
    override val homeShouldPerformAccountSetup: Boolean = false
    override val homeShouldCreateSaldoAccount: Boolean = false
    override val homeShouldUseDynamicBusinessTitle: Boolean = false
    override val homeShouldHandlePartnerOrderDetails: Boolean = false
    override val homepageSchemaFailsafeKey: String = HOMEPAGE_SCHEMA_FAILSAFE
    override val homeShouldShowPartnerActivationMessage: Boolean = false
    override val homeShouldShowActivationBottomSheet: Boolean = false
    override val shouldShowSettingsButton: Boolean = true
    override val isUsingMiniatmProTickerStyling: Boolean = false
    override val homepageSchemaKey: String
        get() = if (Utils.isCardReader()) {
            EDC_SAKU_HOMEPAGE_SCHEMA
        } else {
            EDC_HOMEPAGE_SCHEMA
        }

    override val remoteConfigDefaults: Map<String, Any>
        get() = mapOf(
            EDC_HOMEPAGE_SCHEMA to HOMEPAGE_SCHEMA_VAL,
            EDC_SAKU_HOMEPAGE_SCHEMA to EDC_SAKU_HOMEPAGE_SCHEMA_VAL,
            HOMEPAGE_SCHEMA_FAILSAFE to HOMEPAGE_SCHEMA_VAL_FAILSAFE,
            SAKU_APP_UPDATE_VERSION_CODE to SAKU_APP_UPDATE_VERSION_CODE_VAL,
            EDC_SAKU_ORDER_WELCOME_SCREEN to EDC_SAKU_ORDER_WELCOME_SCREEN_FAILSAFE,
            EDC_KOMISI_AGEN to EDC_KOMISI_AGEN_VAL
        )

    companion object {
        // Schema keys
        const val EDC_HOMEPAGE_SCHEMA = "edc_homepage_schema"
        const val EDC_SAKU_HOMEPAGE_SCHEMA = "edc_saku_homepage_schema"
        const val HOMEPAGE_SCHEMA_FAILSAFE = "homepage_schema_failsafe"

        // Schema default values
        const val HOMEPAGE_SCHEMA_VAL = """
        [
  {
    "block_name": "edc_homepage_saldo",
    "block_start_version": 0,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 1
  },
  {
    "block_name": "edc_homepage_history",
    "block_start_version": 5,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 2
  },
  {
    "block_name": "edc_homepage_card",
    "block_start_version": 0,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 3
  },
  {
    "block_name": "edc_homepage_ppob",
    "block_start_version": 25,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 4
  }
]
    """

        const val EDC_SAKU_HOMEPAGE_SCHEMA_VAL = """
        [
  {
    "block_name": "edc_homepage_saldo",
    "block_start_version": 0,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 1
  },
  {
    "block_name": "edc_saku_homepage_history",
    "block_start_version": 5,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 4
  },
  {
    "block_name": "edc_saku_homepage_card",
    "block_start_version": 0,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 5
  },
  {
    "block_name": "edc_saku_homepage_order",
    "block_start_version": 23800,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 3
  },
  {
    "block_name": "edc_homepage_ppob",
    "block_start_version": 25,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 7
  },
  {
    "block_name": "edc_homepage_banners",
    "block_start_version": 20000,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 2
  },
  {
    "block_name": "edc_komisi_agen",
    "block_start_version": 24600,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 6
  }
]
    """

        const val HOMEPAGE_SCHEMA_VAL_FAILSAFE = """
        [
  {
    "block_name": "edc_homepage_saldo",
    "block_start_version": 0,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 1
  },
  {
    "block_name": "edc_homepage_history",
    "block_start_version": 5,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 2
  },
  {
    "block_name": "edc_homepage_card",
    "block_start_version": 0,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 3
  },
  {
    "block_name": "edc_homepage_ppob",
    "block_start_version": 7,
    "block_end_version": -1,
    "is_visible": true,
    "show_on_verifone": true,
    "show_on_pax": true,
    "rank": 4
  }
]
    """

        // Saku-specific features
        const val SAKU_APP_UPDATE_VERSION_CODE = "saku_app_update_version_code"
        const val SAKU_APP_UPDATE_VERSION_CODE_VAL = """
            {
                "hardUpdateVersionCode": 0,
                 "softUpdateVersionCode": 0
            }
        """

        const val EDC_SAKU_ORDER_WELCOME_SCREEN = "edc_saku_order_welcome_screen"
        const val EDC_SAKU_ORDER_WELCOME_SCREEN_FAILSAFE = """
            {
      "edc_screen_title": "Saatnya mulai #BebasTambahCuan Pakai EDC BukuAgen!",
      "edc_screen_image": "",
      "edc_screen_bullet_points": [
        {
          "icon_url": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/ic_transfer.webp",
          "description": "<b>Transfer</b>・Layani transaksi transfer ke sesama atau beda bank"
        },
        {
          "icon_url": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/ic_check.webp",
          "description": "<b>Tarik Tunai &amp; Cek Saldo・</b>Layani transaksi tarik tunai &amp; cek saldo dari rekening pelanggan"
        },
        {
          "icon_url": "https://storage.googleapis.com/temp-image-test/cash_withdraw.png",
          "description": "<b>PKH・</b>Layani transaksi pencarian &amp; cek saldo dana bantuan PKH"
        }
      ],
      "edc_screen_learn_more": {
        "text": "Pelajari lebih lanjut",
        "redirection_url": "https://bukuwarung.com/bukuagen/edc-saku/"
      },
      "edc_screen_buttons": [
        {
          "text": "Beli EDC",
          "redirection_url": "https://api-v4.bukuwarung.com/mx-mweb/edc/dashboard?entryPoint=BUKUAGEN",
          "is_visisble": true
        },
        {
          "text": "Saya Sudah Beli",
          "redirection_url": "https://api-v4.bukuwarung.com/mx-mweb/edc/landing/external?entryPoint=BUKUAGEN/BUKUWARUNG",
          "is_visible": false
        }
      ]
    }
        """

        const val EDC_KOMISI_AGEN = "edc_komisi_agen"
        const val EDC_KOMISI_AGEN_VAL = """
            {
              "body_name": "edc_komisi_agen",
              "body_analytics_name": "order",
              "body_title": "Komisi Agen",
              "body_subtitle": "",
              "body_rank": 3,
              "body_type": 7,
              "is_visible": true,
              "show_on_verifone": false,
              "show_on_pax": false,
              "start_version": 24500,
              "end_version": -1,
              "deeplink_type": "mweb",
              "deeplink_url": "https://api-v4.bukuwarung.com/mx-mweb/loyalty/v2?entryPoint=BUKUAGEN"
            }
        """

        const val KOMISI_AGEN_TERMS_AND_CONDITIONS = "komisi_agen_terms_and_conditions"
        const val KOMISI_AGEN_DASHBOARD = "komisi_agen_dashboard"
    }
}
