package com.bukuwarung.edc.deeplink

import android.os.Bundle
import android.util.Log
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.app.config.VariantConfig
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.homepage.constant.HomePageRemoteConfig
import com.bukuwarung.edc.homepage.data.model.HomePageBody
import com.bukuwarung.edc.homepage.data.model.HomePageSchema
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity.Companion.EDC_MINIATMPRO_HOMEPAGE_CARD
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity.Companion.EDC_NUSACITA_HOMEPAGE_CARD
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity.Companion.EDC_SAKU_HOMEPAGE_CARD
import com.bukuwarung.edc.login.ui.LoginActivity
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.RemoteConfigUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.bukuwarung.network.refreshtoken.TokenManager
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class DeeplinkHandlerActivity : AppCompatActivity() {

    @Inject
    lateinit var variantConfig: VariantConfig

    private val viewModel: DeeplinkHandlerViewModel by viewModels()
    private val versionCode by lazy { BuildConfig.VERSION_CODE }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        viewModel.fetchAllData()
        setupObservers()
    }

    private fun setupObservers() {
        lifecycleScope.launch {
            combine(
                viewModel.haveSakuDevice,
                viewModel.isWaitingForOps
            ) { haveSakuDevice, isWaitingForOps ->
                Pair(haveSakuDevice, isWaitingForOps)
            }.collect { (haveSakuDevice, isWaitingForOps) ->
                if (haveSakuDevice != null && isWaitingForOps != null) {
                    handleDeeplink()
                }
            }
        }
    }

    private fun handleDeeplink() {
        val uri = intent.data
        if (uri == null) {
            finish()
            return
        }

        if (!isUserAuthenticated()) {
            redirectToLogin(uri.toString())
            return
        }

        checkTokenAndProceed {
            routeToDestination(uri.host)
        }
    }

    private fun isUserAuthenticated(): Boolean {
        val token = EncryptedPreferencesHelper.get(BUKUWARUNG_TOKEN, "")
        return token.isNotEmpty()
    }

    private fun redirectToLogin(deeplink: String) {
        goToDestination(LoginActivity::class.java)
    }

    private fun checkTokenAndProceed(onComplete: () -> Unit) {
        val currentToken = TokenManager.getAccessToken()

        if (currentToken.isNullOrEmpty() || Utils.isTokenExpired()) {
            lifecycleScope.launch {
                redirectToLogin(intent.data.toString())
            }
        } else {
            onComplete()
        }
    }

    private fun routeToDestination(host: String?) {
        val tileConfigs = getTileConfigurationData()
        Log.v("DeeplinkHandlerActivity", "tileConfigs: $tileConfigs")

        when (host) {
            DEEPLINK_CASH_WITHDRAWAL -> {
                handleTileDeeplink("cash_withdrawal", tileConfigs)
            }

            DEEPLINK_TRANSFER -> {
                handleTileDeeplink("transfer", tileConfigs)
            }

            DEEPLINK_BALANCE_INQUIRY -> {
                handleTileDeeplink("balance_check", tileConfigs)
            }

            DEEPLINK_TRANSACTION_HISTORY -> {
                handleTileDeeplink("card_transaction_history", tileConfigs)
            }

            DEEPLINK_SETTINGS -> {
                handleTileDeeplink("settings", tileConfigs)
            }

            else -> {
                finish()
            }
        }
    }

    private fun handleTileDeeplink(
        analyticsName: String,
        tileConfigs: List<Pair<String, HomePageBody?>>
    ) {
        // Find the tile configuration that matches the analytics name
        tileConfigs.forEach { (blockName, homePageBody) ->
            homePageBody?.bodyContent?.find {
                it?.analyticsName == analyticsName
            }?.let { tileItem ->
                // Found matching tile item, now route to it
                val deeplink = tileItem.deeplinkUrl
                val deeplinkType = tileItem.deeplinkType
                val checkIfRegistered = tileItem.checkIfRegistered

                when (deeplinkType) {
                    "app" -> {
                        // Handle app deeplink
                        handleTileFragmentOnItemClickListener(
                            blockName,
                            deeplinkType,
                            deeplink ?: "",
                            checkIfRegistered
                        )
                    }

                    "webview" -> {
                        // Handle webview deeplink
                        goToDestination(Utils.getRouterClass()) {
                            putString("type", deeplinkType)
                            putString("url", deeplink)
                        }
                    }

                    else -> {
                        Log.w("DeeplinkHandler", "Unknown deeplink type: $deeplinkType")
                    }
                }
                return@forEach // Exit the loop once we find a match
            }
        }

        // If no matching tile found, log and finish
        Log.w("DeeplinkHandler", "No tile configuration found for analytics name: $analyticsName")
        finish()
    }

    private fun checkSakuDeviceRegisteredOROpenRouterActivity(
        type: String,
        url: String,
        checkIfRegistered: Boolean
    ) {
        val haveSakuDevice = viewModel.getCurrentHaveSakuDevice()
        val isWaitingForOps = viewModel.getCurrentIsWaitingForOps()

        if (checkIfRegistered && Utils.isCardReader() && haveSakuDevice == false) {
            finish()
            return
        }
        if (checkIfRegistered && variantConfig.shouldWaitForOpsOnDeeplink &&
            isWaitingForOps == true
        ) {
            finish()
            return
        }

        goToDestination(Utils.getRouterClass()) {
            putString("type", type)
            putString("url", url)
        }
    }

    private fun handleTileFragmentOnItemClickListener(
        bodyName: String?,
        type: String,
        url: String,
        checkIfRegistered: Boolean
    ) {
        when (bodyName) {
            EDC_SAKU_HOMEPAGE_CARD, EDC_MINIATMPRO_HOMEPAGE_CARD, EDC_NUSACITA_HOMEPAGE_CARD -> {
                checkSakuDeviceRegisteredOROpenRouterActivity(type, url, checkIfRegistered)
            }

            else -> {
                goToDestination(Utils.getRouterClass()) {
                    putString("type", type)
                    putString("url", url)
                }
            }
        }
    }

    private fun getTileConfigurationData(): List<Pair<String, HomePageBody?>> {
        // Fetch and populate from remote config
        var homePageSchema = HomePageRemoteConfig.getHomePageSchema()
        val type = object : TypeToken<List<HomePageSchema>>() {}.type
        val gson: Gson = GsonBuilder().create()
        var homeData: List<HomePageSchema> = listOf()

        try {
            Log.d("homepage", "fetch homepage json $homePageSchema")
            homeData = gson.fromJson(homePageSchema, type)
        } catch (e: Exception) {
            Log.d("homepage", "fetch failsafe data")
            homePageSchema = RemoteConfigUtils.remoteConfig.getString(
                AppConfig.current.homeConfig.homepageSchemaFailsafeKey
            )
            homeData = gson.fromJson(homePageSchema, type)
        }

        // Filter by device type
        homeData = if (EdcApplication.isVerifoneDevice) {
            homeData.filter { it.shouldShowOnVerifone }
        } else {
            homeData.filter { it.shouldShowOnPax }
        }

        // Filter by visibility and version, then sort by rank
        homeData = homeData.filter {
            it.isVisible && (
                it.blockEndVersion >= versionCode ||
                    it.blockEndVersion == -1
                ) && it.blockStartVersion <= versionCode
        }.sortedBy { it.rank }

        // Extract only tile-related configurations (bodyType == 2)
        return homeData.mapNotNull { data ->
            val bodyName = data.blockName
            val bodyContents = bodyName?.let { retrieveBodyContent(it) }

            // Only return data for tile fragments (bodyType == 2)
            if (bodyContents?.bodyType == 2) {
                Pair(bodyName, bodyContents)
            } else {
                null
            }
        }
    }

    private fun retrieveBodyContent(bodyName: String): HomePageBody {
        val type = object : TypeToken<HomePageBody>() {}.type
        val gson: Gson = GsonBuilder().create()
        val bodyData: HomePageBody

        val bodyContents: String = HomePageRemoteConfig.getHomePageBody(bodyName)
        bwLog("HomePageActivity-fromJson get body data $bodyContents $bodyName")
        bodyData = gson.fromJson(bodyContents, type)

        if (EdcApplication.isVerifoneDevice) {
            bodyData.bodyContent = bodyData.bodyContent?.filter { it?.shouldShowOnVerifone == true }
        } else {
            bodyData.bodyContent = bodyData.bodyContent?.filter { it?.shouldShowOnPax == true }
        }
        bodyData.bodyContent = bodyData.bodyContent?.filter {
            it?.isVisible == true && (
                it.endVersion >= versionCode ||
                    it.endVersion == -1
                ) && it.startVersion <= versionCode
        }
        return bodyData
    }

    companion object {
        const val DEEPLINK_CASH_WITHDRAWAL = "cash-withdrawal"
        const val DEEPLINK_TRANSFER = "transfer"
        const val DEEPLINK_BALANCE_INQUIRY = "balance-inquiry"
        const val DEEPLINK_TRANSACTION_HISTORY = "transaction-history"
        const val DEEPLINK_SETTINGS = "settings"
    }
}
