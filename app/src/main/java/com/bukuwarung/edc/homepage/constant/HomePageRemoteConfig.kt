package com.bukuwarung.edc.homepage.constant

import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.app.config.home.BukuHomeConfig
import com.bukuwarung.edc.homepage.data.model.EdcLeaderboardConfigContents
import com.bukuwarung.edc.util.RemoteConfigUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import timber.log.Timber

object HomePageRemoteConfig {

    // Shared body content keys
    const val EDC_HOMEPAGE_SALDO = "edc_homepage_saldo"
    const val EDC_HOMEPAGE_TICKER = "edc_homepage_ticker"
    const val EDC_HOMEPAGE_HISTORY = "edc_homepage_history"
    const val EDC_HOMEPAGE_CARD = "edc_homepage_card"
    const val EDC_HOMEPAGE_TILES_PER_ROW = "edc_homepage_tiles_per_row"

    // Shared utilities
    const val HOUR = "hour"
    const val MINS = "mins"

    // Shared features
    const val EDC_LEADERBOARD_CONFIG_NAME = "edc_leaderboard"
    const val TICKER_CHOOSE_ACCOUNT = "ticker_choose_account"

    // Shared body content default values
    const val HOMEPAGE_TICKER = """
        {
  "body_name": "edc_homepage_ticker",
  "body_analytics_name": "ticker",
  "body_title": "",
  "body_subtitle": "",
  "body_rank": 1,
  "body_type": 3,
  "body_content": [
    {
      "display_name": "",
      "analytics_name": "",
      "icon": "",
      "rank": 1,
      "is_visible": true,
      "start_version": 0,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "",
      "is_new": true
    }
  ]
}
    """

    const val HOMEPAGE_HISTORY = """
        {
  "body_name": "edc_homepage_history",
  "body_analytics_name": "history",
  "body_title": "Riwayat Transaksi",
  "body_subtitle": "",
  "body_rank": 3,
  "body_type": 4,
  "body_content": [
    {
      "display_name": "Transaksi Digital",
      "analytics_name": "view_digital_transactions",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/clock.png",
      "rank": 1,
      "is_visible": true,
      "start_version": 5,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.payments.ui.history.OrderHistoryActivity",
      "show_on_verifone": true,
      "show_on_pax": true,
      "is_new": true
    },
    {
      "display_name": "Transaksi MiniATM",
      "analytics_name": "view_card_transactions",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/credit-card.png",
      "rank": 2,
      "is_visible": true,
      "start_version": 5,
      "end_version": -1,
      "show_on_verifone": true,
      "show_on_pax": true,
      "deeplink_type": "app",     
      "deeplink_url": "com.bukuwarung.edc.card.ui.edcdevices.ui.DeviceListActivity",
      "is_new": true
    },
    {
      "display_name": "Transaksi MiniATM",
      "analytics_name": "view_card_transactions",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/credit-card.png",
      "rank": 3,
      "is_visible": true,
      "start_version": 5,
      "end_version": -1,
      "show_on_verifone": false,
      "show_on_pax": true,
      "deeplink_type": "mweb",
      "deeplink_url": "https://api-v4.bukuwarung.com/mx-mweb/edc/dashboard",
      "is_new": true
    }
  ],
  "deeplink_type": "app",
  "deeplink_url": "com.bukuwarung.edc.homepage.ui.history.HistoryActivity"
}
    """

    const val HOMEPAGE_CARD = """
        {
  "body_name": "edc_homepage_card",
  "body_analytics_name": "cards",
  "body_title": "MiniATM",
  "body_subtitle": "",
  "body_rank": 3,
  "body_type": 2,
  "no_of_columns": 3,
  "body_content": [
    {
      "display_name": "Transfer",
      "analytics_name": "transfer",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/Transfer.png",
      "rank": 2,
      "is_visible": true,
      "start_version": 0,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.card.ui.CardReaderInstructionActivity?mode=TRANSFER_INQUIRY",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Cek Saldo",
      "analytics_name": "balance_check",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/Check.png",
      "rank": 1,
      "is_visible": true,
      "start_version": 0,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.card.ui.CardReaderInstructionActivity?mode=BALANCE_INQUIRY",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Riwayat Transaksi Kartu",
      "analytics_name": "card_transaction_history",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/History%20Card.png",
      "rank": 3,
      "is_visible": true,
      "start_version": 0,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.card.ui.edcdevices.ui.DeviceListActivity",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    }
  ]
}
    """

    const val HOMEPAGE_SALDO_BODY_CONTENT = """
        {
  "body_name": "edc_homepage_saldo",
  "body_analytics_name": "saldo",
  "body_title": "Transaksi Tanpa Kartu",
  "body_subtitle": "",
  "body_rank": 1,
  "body_type": 1,
  "no_of_columns": 2,
  "body_content": [
    {
      "display_name": "Top Up",
      "analytics_name": "saldo_topup",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/plus-circle.png",
      "rank": 1,
      "is_visible": true,
      "start_version": 5,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.payments.ui.saldo.SaldoTopupActivity",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    },
    {
      "display_name": "Bayar",
      "analytics_name": "payment_out",
      "icon": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/trailing-icon.png",
      "rank": 2,
      "is_visible": true,
      "start_version": 5,
      "end_version": -1,
      "deeplink_type": "app",
      "deeplink_url": "com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity",
      "is_new": true,
      "show_on_verifone": true,
      "show_on_pax": true
    }
  ]
}
    """

    const val EDC_LEADERBOARD_CONFIG_VAL = """
        {
  "campaign_name": "EDC_LEADERBOARD_CAMPAIGN",
  "banner_url": "https://bw-mx-static-prod-image-jk.bukuwarung.com/android/home-page/leaderboard_banner.png",
  "redirection_url": "https://api-v4.bukuwarung.com/mx-mweb/edc/campaign/leaderboard?campaign=EDC_LEADERBOARD_CAMPAIGN"
}
    """

    const val CHOOSE_ACCOUNT_TICKER_DEFAULT = """
        {
          "end_time": "2025-07-07T06:00:00",
          "ticker_message": "Mohon maaf Juragan, layanan cek saldo Bank BNI sedang gangguan.",
          "show_on_cash_withdraw": false,
          "show_on_transfer": false,
          "show_on_check_balance": false
        }
    """

    // Helper methods
    fun getHomePageSchema() = RemoteConfigUtils.remoteConfig.getString(
        AppConfig.current.homeConfig.homepageSchemaKey
    )

    fun getAppUpdateVersionCode() = RemoteConfigUtils.remoteConfig.getString(
        BukuHomeConfig.SAKU_APP_UPDATE_VERSION_CODE
    )

    fun getLeaderboardInfo(): EdcLeaderboardConfigContents = try {
        val type = object : TypeToken<EdcLeaderboardConfigContents>() {}.type
        val json =
            RemoteConfigUtils.remoteConfig.getString(EDC_LEADERBOARD_CONFIG_NAME)
        Gson().fromJson(json, type)
    } catch (ex: Exception) {
        Timber.e(ex)
        EdcLeaderboardConfigContents()
    }

    fun getHomePageBody(bodyName: String) =
        RemoteConfigUtils.remoteConfig.getString(bodyName).ifBlank { "{}" }

    fun getNumberOfColumns() = RemoteConfigUtils.remoteConfig.getString(EDC_HOMEPAGE_TILES_PER_ROW)

    fun getTickerFragmentData(): String = RemoteConfigUtils.remoteConfig.getString(
        AppConfig.current.variantConfig.tickerFragmentName
    )

    fun getEdcOrderWelcome() = RemoteConfigUtils.remoteConfig.getString(
        BukuHomeConfig.EDC_SAKU_ORDER_WELCOME_SCREEN
    )

    fun getKomisiAgenDashboardUrl() = RemoteConfigUtils.remoteConfig.getString(
        BukuHomeConfig.KOMISI_AGEN_DASHBOARD
    )

    fun getKomisiAgenTermsAndConditionsUrl() = RemoteConfigUtils.remoteConfig.getString(
        BukuHomeConfig.KOMISI_AGEN_TERMS_AND_CONDITIONS
    )

    fun getTickerChooseAccountData(): String =
        RemoteConfigUtils.remoteConfig.getString(TICKER_CHOOSE_ACCOUNT)
}
