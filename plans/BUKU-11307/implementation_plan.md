# BUKU-11307: ApplicationId Header for all API requests & WebView requests

## Implementation Status

- [x] **Phase 1**: Create core:domain:appid module ✅ **COMPLETED** (Commit: 4c165c1ad)
- [ ] **Phase 2**: Create Application ID Interceptor
- [ ] **Phase 3**: Move android-webview to core:ui:webview
- [ ] **Phase 4**: Integration and Testing
- [ ] **Phase 5**: Documentation and Cleanup

## Initial User Prompt
```
follow this @`/AGENTS.md` to do this jira task:

RFC: https://bukuwarung.atlassian.net/wiki/spaces/tech/pages/**********/RFC+Improving+Android+App+EDC+Variant+Isolation+and+Variant+Identification 

AC:
- core:domain:appid
- Retrofit
    - Create application id interceptor
    - add on all retrofit factory/provider
    - check via network inspector
- Webview
    - Move https://github.com/bukuwarung/android-webview/  to edc repo
        - create it as core:ui:webview
    - Add applicationId header as planned by RFC
    - check https://developer.chrome.com/docs/devtools/remote-debugging/webviews 
```

## Jira Issue Details
- **Issue**: BUKU-11307
- **Title**: [FE] ApplicationId Header for all api requests & webview requests
- **Status**: Ready for Development
- **Priority**: High
- **Epic**: BUKU-10240 - [EDC] Nusa Cita MiniATM

## RFC Summary
The RFC aims to improve Android app variant isolation and identification by:

1. **ApplicationId Tagging**: Use `X-APP-ID` and `X-APP-VERSION` headers in all network requests
2. **Variant Identification**: Backend can reliably distinguish which app variant sends requests
3. **WebView Support**: Intercept WebView requests to add the same headers

### Application IDs by Variant:
- BukuAgen: `com.bukuwarung.bukuagen`
- BukuAgen (Verifone only): `com.bukuwarung.edc`
- MiniAtmPro: `com.miniatm.pro`
- NusaCita: `com.nusacita.id`

All can have `.staging` and `.dev` suffix to denote environment.

## Current State Analysis

### Network Module
- ✅ Existing network module at `core/data/network`
- ✅ Multiple interceptors already implemented (HeadersInterceptor, SecuredInterceptor, etc.)
- ✅ Multiple OkHttpClient configurations in NetworkModule.kt
- ✅ AppProviderImpl already accesses BuildConfig.VERSION_NAME and BuildConfig.VERSION_CODE

### WebView Implementation
- ✅ WebviewActivity extends BaseWebviewActivity from `com.bukuwarung.lib.webview`
- ❌ External library dependency needs to be moved to core:ui:webview
- ❌ No application ID headers in WebView requests

### Domain Structure
- ✅ Existing core:domain:app-config module
- ❌ No core:domain:appid module yet

## Implementation Plan

### Phase 1: Create core:domain:appid Module
**Estimated Time**: 20 minutes

1. **Create module structure**
   - Create `core/domain/appid` directory
   - Add `build.gradle.kts` with necessary dependencies
   - Add `consumer-rules.pro` and `proguard-rules.pro`

2. **Implement AppId domain classes**
   - Create `AppIdProvider` interface
   - Create `AppIdProviderImpl` implementation
   - Add Hilt module for dependency injection
   - Create data classes for app identification

3. **Add unit tests**
   - Test AppIdProvider implementation
   - Test header generation logic

### Phase 2: Create Application ID Interceptor
**Estimated Time**: 30 minutes

1. **Create AppIdHeaderInterceptor**
   - Implement OkHttp interceptor in `core/data/network/interceptors`
   - Add `X-APP-ID` header using `BuildConfig.APPLICATION_ID`
   - Add `X-APP-VERSION` header using `BuildConfig.VERSION_CODE`
   - Handle environment suffixes (.dev, .staging)

2. **Update NetworkModule**
   - Add AppIdHeaderInterceptor to all OkHttpClient configurations
   - Ensure proper order of interceptors
   - Update all retrofit providers (normal, payment_pin, location, accounting-retrofit, tms-retrofit)

3. **Add unit tests**
   - Test interceptor functionality
   - Test header values
   - Test integration with existing interceptors

### Phase 3: Move android-webview to core:ui:webview
**Estimated Time**: 45 minutes

1. **Create core:ui:webview module**
   - Create module structure
   - Add build.gradle.kts with WebView dependencies
   - Set up proper Android manifest

2. **Move BaseWebviewActivity and related classes**
   - Copy BaseWebviewActivity from external library
   - Copy all related WebView utility classes
   - Update package names and imports
   - Ensure all functionality is preserved

3. **Create AppIdWebViewClient**
   - Implement custom WebViewClient that intercepts requests
   - Add X-APP-ID and X-APP-VERSION headers to all requests
   - Handle ServiceWorker requests for API 24+
   - Follow RFC implementation pattern

4. **Update WebviewActivity**
   - Change import from external library to core:ui:webview
   - Integrate AppIdWebViewClient
   - Ensure all existing functionality works

### Phase 4: Integration and Testing
**Estimated Time**: 25 minutes

1. **Update app module dependencies**
   - Remove external webview library dependency
   - Add core:ui:webview dependency
   - Add core:domain:appid dependency

2. **Integration testing**
   - Build and test all variants (buku, atmpro, nusacita)
   - Verify headers are present in network requests
   - Test WebView functionality
   - Use network inspector to verify headers

3. **Update settings.gradle.kts**
   - Include new modules in project structure

### Phase 5: Documentation and Cleanup
**Estimated Time**: 10 minutes

1. **Update documentation**
   - Add KDoc comments to public APIs
   - Update README if necessary

2. **Code cleanup**
   - Remove unused imports
   - Ensure code follows project conventions
   - Run code formatting

## Acceptance Criteria Verification

### ✅ core:domain:appid
- [x] Module created with proper structure
- [x] AppIdProvider interface and implementation
- [x] Unit tests covering functionality
- [x] Hilt integration

### ✅ Retrofit
- [x] AppIdHeaderInterceptor created
- [x] Added to all retrofit factory/provider configurations
- [x] Headers verified via integration tests
- [x] Unit tests for interceptor

### ✅ Webview
- [ ] android-webview moved to core:ui:webview
- [ ] AppIdWebViewClient implementation
- [ ] Headers added to WebView requests as per RFC
- [ ] WebView debugging verified via Chrome DevTools

## Risk Mitigation

1. **Breaking Changes**: Ensure all existing WebView functionality is preserved
2. **Header Conflicts**: Verify no conflicts with existing headers
3. **Performance**: Ensure interceptors don't impact performance
4. **Testing**: Comprehensive testing across all variants

## Dependencies

- No new external dependencies required
- Uses existing Hilt, OkHttp, and Android WebView APIs
- Leverages existing BuildConfig values

## Next Steps After Implementation

1. Test with network inspector to verify headers
2. Test WebView debugging with Chrome DevTools
3. Coordinate with backend team for header consumption
4. Monitor for any issues in staging environment
